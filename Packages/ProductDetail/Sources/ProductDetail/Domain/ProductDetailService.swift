import AsyncAlgorithms
import Combine
import Foundation
import NativeAPI
import OGAppKitSDK
import OGCore
import OGRouter

typealias OGProductDetailComponent = Skie.com_ottogroup_ogappkit_nativeui__api.ProductDetailComponent.__Sealed
typealias OGProductDetailScreen = SkieSwiftFlow<OGResult<ProductDetailScreen>>

// MARK: - ProductDetailServing

protocol ProductDetailServing {
  func fetchProductDetail(productId: String, secondaryId: String?, componentConfigsJson: String) async
  func fetchProductDetailScreen(for url: URL, componentConfigsJson: String) async
  func addProductToBasket(id: String) async throws -> Int
  func addVoucherToBasket(id: String, customName: String?) async throws -> Int
  func addProductToWishlist(id: String) async throws -> [String]
  func refreshWishlistCount() async throws -> [String]
  func refreshBasketCount() async throws -> Int
  func removeProductFromWishlist(id: String) async throws -> [String]
  func updateProductColorSelection(productId: String, screenId: String) async
  func updateProductVariantSelection(productId: String, screenId: String) async
  var successStream: AsyncChannel<(components: [OGProductDetailComponent], screenId: String)> { get async }
  var errorStream: AsyncChannel<Error> { get async }
}

// MARK: - ProductDetailService

actor ProductDetailService: ProductDetailServing {
  let successStream: AsyncChannel<(components: [OGProductDetailComponent], screenId: String)> = .init()
  let errorStream: AsyncChannel<Error> = .init()
  private let ogNative: OGNative
  private let logger: any OGLoggingDistributable
  private var productIdTask: Task<Void, Never>?
  private var productRouteTask: Task<Void, Never>?
  private var currentScreenId: String?

  init(
    ogNative: OGNative = NativeAPIContainer.shared.nativeAPI(),
    logger: any OGLoggingDistributable = OGCoreContainer.shared.logger()
  ) {
    self.ogNative = ogNative
    self.logger = logger
  }

  func fetchProductDetail(productId: String, secondaryId: String?, componentConfigsJson: String) async {
    fetchProductDetailScreen(productId: productId, secondaryId: secondaryId, componentConfigsJson: componentConfigsJson)
  }

  func fetchProductDetailScreen(for url: URL, componentConfigsJson: String) async {
    fetchProductDetailScreen(url: url, componentConfigsJson: componentConfigsJson)
  }

  func updateProductColorSelection(productId: String, screenId: String) async {
    let context = ProductDetailScreenRequestContextFromColorSelection()
    updateProductDetailScreen(screenId: screenId, productId: productId, context: context)
  }

  func updateProductVariantSelection(productId: String, screenId: String) async {
    let context = ProductDetailScreenRequestContextFromVariantSelection()
    updateProductDetailScreen(screenId: screenId, productId: productId, context: context)
  }

  func addVoucherToBasket(id: String, customName: String?) async throws -> Int {
    let result = try await ogNative.addVoucherToBasket(id: id, customName: customName)
    return try basket(result: result, id: id)
  }

  func addProductToBasket(id: String) async throws -> Int {
    let result = try await ogNative.addProductToBasket(id: id)
    return try basket(result: result, id: id)
  }

  func addProductToWishlist(id: String) async throws -> [String] {
    let result = try await ogNative.addProductToWishlist(id: id)
    switch onEnum(of: result) {
    case let .failure(error):
      logger.log(.critical, domain: .service, message: error.failure.asError().localizedDescription)
      throw WishlistError.addToWishlistFailed(id)
    case let .success(wishlist):
      return wishlist.value.items.map {
        if let productId = $0.productId {
          productId
        } else {
          $0.id
        }
      }
    }
  }

  func removeProductFromWishlist(id: String) async throws -> [String] {
    let result = try await ogNative.removeProductFromWishlist(id: id)
    switch onEnum(of: result) {
    case let .failure(error):
      logger.log(.critical, domain: .service, message: error.failure.asError().localizedDescription)
      throw WishlistError.removeFromWishlistFailed(id)
    case let .success(wishlist):
      return wishlist.value.items.map {
        if let productId = $0.productId {
          productId
        } else {
          $0.id
        }
      }
    }
  }

  private func basket(result: OGResult<Basket>, id: String) throws -> Int {
    switch onEnum(of: result) {
    case let .failure(error):
      logger.log(.critical, domain: .service, message: error.failure.asError().localizedDescription)
      if error.failure is OGAppKitSDK.BasketError.Generic {
        throw BasketError.generic(id)
      }
      if error.failure is OGAppKitSDK.BasketError.GiftCardAlreadyInBasket {
        throw BasketError.giftCardAlreadyInBasket
      }
      if error.failure is OGAppKitSDK.BasketError.GiftCardNameTooLong {
        throw BasketError.giftCardNameTooLong
      }
      if error.failure is OGAppKitSDK.BasketError.GiftCardProductAlreadyInBasket {
        throw BasketError.giftCardProductAlreadyInBasket
      }
      if error.failure is OGAppKitSDK.BasketError.GiftCardSameValueAlreadyInBasket {
        throw BasketError.giftCardSameValueAlreadyInBasket
      }
      if error.failure is OGAppKitSDK.BasketError.ItemCountExceeded {
        throw BasketError.itemCountExceeded
      }
      if error.failure is OGAppKitSDK.BasketError.ProductUnavailable {
        throw BasketError.productUnavailable
      }

      throw BasketError.failed(id)

    case let .success(basket):
      return Int(basket.value.items.reduce(0) { $0 + $1.amount })
    }
  }

  private func fetchProductDetailScreen(url: URL, componentConfigsJson: String) {
    productRouteTask?.cancel()
    productIdTask?.cancel()
    let screen = ogNative.getProductDetailScreenByUrl(
      url: url.absoluteString,
      componentConfigsJson: componentConfigsJson
    )
    productRouteTask = observe(detailScreen: screen)
  }

  private func updateProductDetailScreen(screenId: String, productId: String, context: ProductDetailScreenRequestContext) {
    ogNative.updateProductDetailScreen(screenId: screenId, productId: productId, context: context)
  }

  private func fetchProductDetailScreen(productId: String, secondaryId: String?, componentConfigsJson: String) {
    productRouteTask?.cancel()
    productIdTask?.cancel()
    let screen = ogNative.getProductDetailScreen(
      id: productId,
      secondaryId: secondaryId,
      componentConfigsJson: componentConfigsJson
    )
    productIdTask = observe(detailScreen: screen)
  }

  func refreshWishlistCount() async throws -> [String] {
    let result = try await ogNative.getWishlist()
    switch onEnum(of: result) {
    case let .failure(error):
      throw ProductDetailError.unknown(error.failure.asError())
    case let .success(wishlist):
      return wishlist.value.items.map {
        if let productId = $0.productId {
          productId
        } else {
          $0.id
        }
      }
    }
  }

  func refreshBasketCount() async throws -> Int {
    let result = try await ogNative.getBasket()
    switch onEnum(of: result) {
    case let .failure(error):
      throw ProductDetailError.unknown(error.failure.asError())
    case let .success(basket):
      return basket.value.items.count
    }
  }

  private func observe(detailScreen: OGProductDetailScreen) -> Task<Void, Never> {
    Task.detached { [weak self] in
      for await screenResult in detailScreen {
        guard let self else { return }
        switch onEnum(of: screenResult) {
        case let .failure(error):
          await self.errorStream.send(error.failure.asError())
        case let .success(screen):
          // Track the current screen ID for cleanup
          await self.setCurrentScreenId(screen.value.screenId)

          let newComponents = screen.value.components.map { onEnum(of: $0) }
          await self.successStream.send((newComponents, screen.value.screenId))
        }
      }
    }
  }

  /// Sets the current screen ID and cleans up the previous one
  private func setCurrentScreenId(_ screenId: String) {
    // Clean up previous screen if it exists
    if let previousScreenId = currentScreenId, previousScreenId != screenId {
      ogNative.cleanupProductScreen(screenId: previousScreenId)
      logger.log(.debug, domain: .service, message: "Cleaned up previous PDP screen: \(previousScreenId)")
    }
    currentScreenId = screenId
  }

  /// Cleans up KMM resources for the current screen
  func cleanupCurrentScreen() async {
    if let screenId = currentScreenId {
      // Call KMM cleanup method to remove screen state
      ogNative.cleanupProductScreen(screenId: screenId)
      logger.log(.debug, domain: .service, message: "Cleaned up PDP screen: \(screenId)")
      currentScreenId = nil
    }
  }

  deinit {
    productRouteTask?.cancel()
    productIdTask?.cancel()
    successStream.finish()
    errorStream.finish()

    // Clean up KMM resources
    if let screenId = currentScreenId {
      Task {
        await cleanupCurrentScreen()
      }
    }
  }
}

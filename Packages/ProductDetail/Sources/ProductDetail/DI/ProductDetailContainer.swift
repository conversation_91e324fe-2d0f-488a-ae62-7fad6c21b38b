import OGAppKitSDK
import OGDIService
import OGDomainStore
import UICatalog

public final class ProductDetailContainer: OGDISharedContainer {
  public static var shared: ProductDetailContainer = .init()
  public var manager: OGDIContainerManager = .init()

  var service: OGDIService<ProductDetailServing> {
    self {
      ProductDetailService()
    }.unique
  }

  var domainStore: OGDIService<ProductDetailStore> {
    self {
      OGDomainStoreFactory.make(route: .empty)
    }.unique  // Changed from .shared to .unique to prevent memory retention
  }

  var addToBasketSuccessService: OGDIService<AddToBasketSuccessServing> {
    self {
      AddToBasketSuccessService()
    }.unique
  }

  var addToBasketSuccessDomainStore: OGDIService<AddToBasketSuccessStore> {
    self {
      OGDomainStoreFactory.make(productId: "", screenId: nil)
    }.unique
  }

  var priceFormatter: OGDIService<PriceFormattable> {
    self {
      PriceFormatter()
    }.shared
  }

  var buttonStyleResolver: OGDIService<ButtonStyleFactory> {
    self {
      ButtonStyleFactory()
    }.shared
  }

  var basketStore: OGDIService<BasketStore> {
    self {
      OGDomainStoreFactory.make()
    }.unique
  }

  var wishlistStore: OGDIService<WishlistStore> {
    self {
      OGDomainStoreFactory.make()
    }.shared
  }
}
